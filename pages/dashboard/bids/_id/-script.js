
// importing components
import ApplyOffer from '~/components/dashboard/modals/ApplyOffer.vue'
import InviteModal from '~/components/dashboard/modals/InviteModal.vue'
import CloseBid from '~/components/dashboard/modals/CloseBid.vue'
import SubComments from '~/components/dashboard/modals/SubComments.vue'
import GenerateReport from '~/components/dashboard/modals/GenerateReport.vue'
// importing vuex tools
import { mapState } from 'vuex'
import commentMixin from '~/mixins/commentMixin'

export default {
  name: 'BidDetails',
  layout: 'dashboard',
  components: {
    ApplyOffer,
    InviteModal,
    CloseBid,
    SubComments,
    GenerateReport,
  },
  mixins:[commentMixin],
  async asyncData(context) {
    const bid = await context.$axios.$get(`/bids/${context.params.id}/show`)

    return { bid: bid.data }
  },
  data() {
    return {
      comment_form: {
        comment: null,
      },
      subcomment_form: [],
      selected_offer: {},
      selected_user_offer: null,
      user_sidebar_status: false,
      offer_logs: [],
      sucomments_list: null,
      offer_sidebar: false,
      comment_disabled: false,
      selectedTags: [],
      filteredTags: [],
      filteredTagsWithSearch: [],
      invitations_sidebar: false,
      disabled: false,
      disabled_submit_invite: false,
      searchText: '',
      debounceTimeout: null,
      aprroveLoading: false,
      rejectLoading: false,
      autoCloseTimer: null,
    }
  },
  watch: {
    user_sidebar_status(current) {
      if (current == false) {
        this.selected_user_offer = null
      }
    },
    offer_sidebar(current) {
      if (current == false) {
        this.selected_offer = {}
      }
    },
    // Re-setup timer if bid data changes
    'bid.expiresAt'() {
      this.autoCloseBid()
    }
  },
  computed: {
    ...mapState({
      offers: (state) => state.bids.bid_offers,
      comments: (state) => state.bids.bid_comments,
      closes: (state) => state.localStorage.closes,
    }),
    canDelete(){
      return this.bid.permissions.can_delete && this.bid.pending_deletion !== 1
    },
    canApproveDisapprove(){
      return this.bid.pending_deletion === 1 && this.userData.id === this.bid.client.admin_id
    },
    lowestPrice() {
      if (this.offers.length > 0) {
        return this.offers[0].price
      } else {
        return false
      }
    },
  },
  async created() {
    // Set Invited Email
    this.selectedTags = this.bid.invidedSellers
    // fetch offers and comments
    await this.$axios
      .$get(`/offers/${this.$route.params.id}/show`)
      .then((res) => {
        this.$store.commit('bids/SET_OFFERS', res.data)
      })
    await this.$axios
      .$get(`/comments/${this.$route.params.id}/show`)
      .then((res) => {
        // set sub comments v-modal
        res.data.forEach((elem, index) => {
          this.subcomment_form.push({
            comment: null,
            parent_id: elem.id,
            idx: index,
          })
        })
        this.$store.commit('bids/SET_COMMENTS', res.data)
      })
    await this.$axios.$get('/utils/close_reasons').then((res) => {
      this.$store.commit('localStorage/SET_CLOSES', res.data)
    })
  },
  mounted() {
    // subscribe in pusher for offers & comments
    const channel = this.$pusher.subscribe(
      `bid-channel-${this.$route.params.id}`
    )

    channel.bind(`comment-event`, (chat) => {
      if (chat.comment.parent_id == null) {
        this.$store.commit('bids/PUSH_COMMENT', chat.comment)
        this.subcomment_form.push({ comment: null, parent_id: chat.comment.id })
      } else if (chat.comment.parent_id != null) {
        const payload = {
          data: chat.comment,
          id: chat.comment.parent_id,
        }
        this.$store.commit('bids/PUSH_SUB_COMMENT', payload)
      }
    })

    channel.bind(`offer-event`, (offer) => {
      this.$store.commit('bids/SET_OFFERS_PUSHER', offer.offer)
    })
    channel.bind(`updating-offer-event`, (offers) => {
      // this.$store.commit('bids/REMOVE_OFFER_PUSHER', offers.offer)
      this.$axios.$get(`/offers/${this.$route.params.id}/show`).then((res) => {
        this.$store.commit('bids/SET_OFFERS', res.data)
      })
    });

    // Bid deletion events
    channel.bind(`bid-deletion-request-event`, () => {
      // Update bid status to show pending deletion
      this.bid.pending_deletion = 1
      this.TriggerNotify('info', this.$t('admin.bid_deletion_requested'))
    })

    channel.bind(`bid-deletion-approved-event`, () => {
      // Bid deletion was approved - redirect to mybids
      this.TriggerNotify('success', this.$t('admin.bid_deletion_approved'))
      this.$router.push(this.localePath('/dashboard/mybids'))
    })

    channel.bind(`bid-deletion-rejected-event`, () => {
      // Bid deletion was rejected - update status
      this.bid.pending_deletion = 0
      this.TriggerNotify('info', this.$t('admin.bid_deletion_rejected'))
    })

    // Auto close bid if it is expired
    this.autoCloseBid()

    // this.bid.expiresAt = '2025-07-30 10:09:00'
  },
  destroyed() {
    console.log('[AutoCloseBid] Component destroyed, cleaning up timer')
    if (this.autoCloseTimer) {
      console.log('[AutoCloseBid] Clearing existing timer:', this.autoCloseTimer)
      clearTimeout(this.autoCloseTimer)
      this.autoCloseTimer = null
    } else {
      console.log('[AutoCloseBid] No timer to clear')
    }
    this.$pusher.channels.channels = {}
  },
  methods: {
    autoCloseBid() {
      console.log('[AutoCloseBid] ===== Starting autoCloseBid method =====')
      console.log('[AutoCloseBid] Bid data:', {
        id: this.bid.id,
        status: this.bid.status,
        expiresAt: this.bid.expiresAt,
        byMe: this.bid.byMe
      })

      const expireAt = this.bid.expiresAt; // Use actual bid expiry instead of hardcoded
      console.log('[AutoCloseBid] Using expiry date:', expireAt)

      // Clear any existing timer first
      if (this.autoCloseTimer) {
        console.log('[AutoCloseBid] Clearing existing timer:', this.autoCloseTimer)
        clearTimeout(this.autoCloseTimer)
        this.autoCloseTimer = null
      } else {
        console.log('[AutoCloseBid] No existing timer to clear')
      }

      // Validate expiresAt exists and bid is still active
      if (!expireAt) {
        console.log('[AutoCloseBid] No expiry date found, skipping timer setup')
        return
      }

      if (this.bid.status !== 1) {
        console.log('[AutoCloseBid] Bid is not active (status:', this.bid.status, '), skipping timer setup')
        return
      }

      console.log('[AutoCloseBid] Original expiresAt string:', expireAt)
      const expiryDateString = expireAt.replace(' ', 'T')
      console.log('[AutoCloseBid] Formatted expiresAt string:', expiryDateString)

      const expiryDate = new Date(expiryDateString)
      console.log('[AutoCloseBid] Parsed expiry date:', expiryDate)

      // Validate the date is valid
      if (isNaN(expiryDate.getTime())) {
        console.error('[AutoCloseBid] Invalid expiry date:', expireAt)
        return
      }

      const currentDate = new Date()
      const delay = expiryDate - currentDate

      console.log('[AutoCloseBid] Current date:', currentDate)
      console.log('[AutoCloseBid] Expiry date:', expiryDate)
      console.log('[AutoCloseBid] Delay in milliseconds:', delay)
      console.log('[AutoCloseBid] Delay in seconds:', Math.round(delay / 1000))
      console.log('[AutoCloseBid] Delay in minutes:', Math.round(delay / 1000 / 60))
      console.log('[AutoCloseBid] Delay in hours:', Math.round(delay / 1000 / 60 / 60))

      // Only set timer if bid hasn't expired yet
      if (delay > 0 && delay <= 2147483647) {
        console.log('[AutoCloseBid] Setting up timer for', delay, 'milliseconds')
        console.log('[AutoCloseBid] Timer will trigger at:', new Date(Date.now() + delay))

        this.autoCloseTimer = setTimeout(() => {
          console.log('[AutoCloseBid] ===== TIMER TRIGGERED! =====')
          console.log('[AutoCloseBid] Current time:', new Date())
          console.log('[AutoCloseBid] Expected expiry time:', expiryDate)
          console.log('[AutoCloseBid] Current bid status:', this.bid.status)

          // Double-check the bid is still active before refreshing
          if (this.bid.status === 1) {
            console.log('[AutoCloseBid] Bid is still active, refreshing page...')
            this.$nuxt.refresh()
          } else {
            console.log('[AutoCloseBid] Bid is no longer active, skipping refresh')
          }
        }, delay)

        console.log('[AutoCloseBid] Timer set with ID:', this.autoCloseTimer)
      } else if (delay <= 0) {
        console.log('[AutoCloseBid] Bid has already expired (delay:', delay, '), refreshing immediately')
        this.$nuxt.refresh()
      } else {
        console.log('[AutoCloseBid] Delay exceeds setTimeout limit (', delay, 'ms), skipping timer setup')
      }

      console.log('[AutoCloseBid] ===== autoCloseBid method completed =====')
    },
    handleInput() {
      clearTimeout(this.debounceTimeout)

      this.debounceTimeout = setTimeout(() => {
        this.filterItems()
      }, 500)
    },
    filterItems() {
      this.filteredTagsWithSearch = this.filteredTags.filter((item) => {
        return item.email.toLowerCase().includes(this.searchText.toLowerCase())
      })

      // Check if the search text is a valid email and doesn't exist in the filtered results
      if (this.searchText && this.isValidEmail(this.searchText)) {
        const emailExists = this.filteredTagsWithSearch.some(item =>
          item.email.toLowerCase() === this.searchText.toLowerCase()
        )

        if (!emailExists) {
          // Add the searched email as a selectable option
          this.filteredTagsWithSearch.unshift({
            email: this.searchText,
            company_name: '',
            logo: null,
            isNewEmail: true // Flag to identify this as a new email
          })
        }
      }
    },
    isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(email)
    },
    handleGenerateReport(item) {
      if (!this.bid.report_url) {
        this.selected_offer = item
        this.$bvModal.show('closeGenerateReport')
      } else {
        this.TriggerNotify('error', this.$t('admin.report_exist'))
        window.open(this.bid.report_url, '_blank')
      }
    },
    handleLockGenerateReport() {
      this.selected_offer = {}
      this.$nuxt.refresh()
    },
    async fetchTags() {
      this.disabled = true
      // Call the API to fetch tags based on the search query
      try {
        const response = await this.$axios.$get(`/bids/suggestions`, {
          params: {
            region_id: this.bid.region_id,
            type_id: this.bid.type_id,
          },
        })
        this.filteredTags = response.data
      } catch (error) {
        console.error('Error fetching tags:', error)
      }
      this.disabled = false
    },
    triggerSuggestion() {
      this.fetchTags()
      this.invitations_sidebar = true
    },
    addTag(tag) {
      // Add the tag to selectedTags if not already added
      if (tag && !this.selectedTags.includes(tag.email)) {
        this.selectedTags.push(tag.email)
      } else if (tag && this.selectedTags.includes(tag.email)) {
        this.selectedTags.splice(this.selectedTags.indexOf(tag.email), 1)
      }
      this.$emit('handle-selected-email', this.selectedTags)
    },
    autoExpand(type) {
      const textarea =
        type == 'maincomment'
          ? document.getElementById('maincomment')
          : document.getElementById('subcomment')
      textarea.style.height = 'auto'
      textarea.style.height = `${textarea.scrollHeight}px`
    },
    openModal(id) {
      if (id == 'offer') {
        this.selected_offer = this.bid.yourOffer
      }
      this.$bvModal.show(id)
    },
    hideModal(id) {
      if (id == 'offer') {
        this.selected_offer = {}
      }
      this.$bvModal.hide(id)
    },
    handleCommentForm() {
      this.$refs.maincomment.validate().then((success) => {
        if (success) {
          this.$axios
            .post(
              `/comments/${this.$route.params.id}/addComment`,
              this.comment_form
            )
            .then((res) => {
              this.$store.dispatch('localStorage/response_handler', res.data)
              if (this.notify.state == 0) {
                this.TriggerNotify('success', this.$t('admin.comment_success'))
                this.$nextTick(() => {
                  this.$refs.maincomment.reset()
                })
                this.comment_form.comment = null
              } else {
                this.TriggerNotify('error', this.notify.message)
              }
            })
        }
      })
    },
    showSubComments(item) {
      this.sucomments_list = item
      this.openModal('subcomments')
    },
    handleSubCommentForm(idx) {
      this.comment_disabled = true
      const form_data = new FormData()
      if (
        this.subcomment_form[idx].comment == null ||
        this.subcomment_form[idx].comment == ''
      ) {
        this.TriggerNotify('error', this.$t('admin.subcommment_error'))
        return true
      }
      form_data.append('parent_id', this.subcomment_form[idx].parent_id)
      form_data.append('comment', this.subcomment_form[idx].comment)
      this.$axios
        .post(`/comments/${this.$route.params.id}/addComment`, form_data)
        .then((res) => {
          this.$store.dispatch('localStorage/response_handler', res.data)
          if (this.notify.state == 0) {
            this.TriggerNotify('success', this.$t('admin.comment_success'))
            this.subcomment_form[idx].comment = null
          } else {
            this.TriggerNotify('error', this.notify.message)
          }
        })
      this.comment_disabled = false
    },
    getUserData(user) {
      this.selected_user_offer = user
    },
    async fetchLog(bidID, offerID) {
      await this.$axios.$get(`offers/${bidID}/log/${offerID}`).then((res) => {
        this.offer_logs = res.data
        this.offer_sidebar = true
      })
    },
    async handleInviteBidderReq() {
      this.disabled_submit_invite = true
      const form_data = new FormData()
      if (this.selectedTags.length < 1) {
        this.TriggerNotify(
          'error',
          this.$t('admin.invite_bidders_validation_min_one')
        )
      } else {
        this.selectedTags.forEach((email) => {
          form_data.append('bidders[]', email)
        })
        await this.$axios
          .post(`/bids/${this.$route.params.id}/inviteBidders`, form_data)
          .then((res) => {
            this.$store.dispatch('localStorage/response_handler', res.data)
            if (this.notify.state == 0) {
              this.TriggerNotify('success', this.$t('admin.invited_success'))
              this.invitations_sidebar = false
              this.$nuxt.refresh()
            } else {
              this.TriggerNotify('error', this.notify.message)
              this.invitations_sidebar = false
              this.disabled_submit_invite = false
            }
          })
      }
      this.disabled_submit_invite = false
    },
    confirmDeleteBid() {
      this.$swal({
        title: this.$t('admin.delete_bid'),
        text: this.$t('admin.delete_bid_desc'),
        icon: 'success',
        confirmButtonText: this.$t('admin.cancel'),
        confirmButtonColor: '#1E805D',
        showDenyButton: true,
        denyButtonText: this.$t('admin.delete'),
      }).then((result) => {
        if (result.isDenied) {
          this.deleteBid()
        }
      })
    },
    async deleteBid() {
      await this.$axios.delete(`bids/${this.bid.id}/delete`).then((res) => {
        this.$store.dispatch('localStorage/response_handler', res.data)
        if (this.notify.state === 0) {
          // Update store to reflect deletion request
          this.$store.commit('bids/UPDATE_BID_DELETION_STATUS', {
            bid_id: this.bid.id,
            pending_deletion: 1
          })
          // this.TriggerNotify('success', this.$t('admin.deleted_success'))
          this.$router.push(this.localePath('/dashboard/mybids'))
        } else {
          this.TriggerNotify('error', this.notify.message)
        }
      })
    },
    async approveDeletion() {
      this.aprroveLoading = true
      await this.$axios
        .post(`/v2/bids/${this.bid.id}/approve-deletion`)
        .then((res) => {
          this.$store.dispatch('localStorage/response_handler', res.data)
          if (this.notify.state == 0) {
            // Remove bid from store lists since it's approved for deletion
            this.$store.commit('bids/REMOVE_BID_FROM_LISTS', this.bid.id)
            // this.TriggerNotify('success', this.$t('admin.deleted_success'))
            this.$router.push(this.localePath('/dashboard/mybids'))
          } else {
            this.TriggerNotify('error', this.notify.message)
          }
        })
      this.aprroveLoading = false
    },
    async rejectDeletion() {
      this.rejectLoading = true
      await this.$axios
        .post(`/v2/bids/${this.bid.id}/reject-deletion`)
        .then((res) => {
          this.$store.dispatch('localStorage/response_handler', res.data)
          if (this.notify.state == 0) {
            // Update store to reflect rejection (reset pending_deletion to 0)
            this.$store.commit('bids/UPDATE_BID_DELETION_STATUS', {
              bid_id: this.bid.id,
              pending_deletion: 0
            })
            // this.TriggerNotify('success', this.$t('admin.deletion_rejects'))
            this.$router.push(this.localePath('/dashboard/mybids'))
          } else {
            this.TriggerNotify('error', this.notify.message)
          }
        })
      this.rejectLoading = false
    },
  },
}
